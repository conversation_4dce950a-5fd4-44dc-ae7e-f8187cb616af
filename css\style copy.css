@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root {
    --primary-color: #ff7800;
    --secondary-color: #7af75b;
    --secondary-dark-color: #79800559;
    --black: #130f40;

    --main_color: #ff8716;
    --p_color: #7b7b7b;
    --bg_color: #f3f3f3;
    --white_color: #fff;
    --color_heading: #121416;
    --border_color: #e5e5e5d5;
    --Sale_color: #E51A1A;


    --light-bg-color: #f2f3f5;
    --light-text-color: #7c899a;
    --border-color: #e5e8ec;
    --dark-color: #0a021c;

    --font-small: 13px;
    --font-smaller: 11px;

    --percent100: 100%;
    --percent50: 50%;

    --fw3: 300;
    --fw5: 500;
    --fw6: 600;
    --fw7: 700;
    --fw8: 800;

    /* --trans-background-color: background-color .3s, color .3s;
    --trans-background: background-color .3s;
    --trans-color: color .3s; */


}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    outline: none;
    border: none;
    text-decoration: none;
    text-transform: capitalize;
    /* text-align: center;  */
    /* list-style: none; */
    font-family: "Inter", sans-serif;
    /* color: var(--color_heading); */
    transition: all .2s linear;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 0.5rem;
    font-size: 66%;
}

.btn {
    display: inline-block;
    padding: 0.8rem 3.8rem;
    font-size: 1.8rem;
    border: 0.1rem solid var(--secondary-color);
    outline: 0;
    background: var(--secondary-color);
    border-radius: 1rem;
    cursor: pointer;
    color: var(--white_color);
    text-align: center;
    color: var(--main_color);
}

.btn:hover {
    background: none;
    color: var(--secondary-color);
}

.heading {
    margin: 5rem 0;
    font-size: 3.5rem;
    text-align: center;
    color: var(--Sale_color);
}

.heading div {
    display: inline-block;
    border-bottom: 0.2rem solid var(--Sale_color);
    width: 15rem;
}

.heading div:hover {
    width: 25rem;
}

section {
    padding: 3rem 9%;
}



/*-----------------------
 * REUSABLE SELECTION End --
  *----------------------*/

/* header  */
header {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1em 3%;
    background: rgba(10, 10, 9, 0.6);
    z-index: 1000;
}

header .logo {
    font-size: 3.2rem;
    color: var(--main_color);
    padding: 0.4rem;
}

header .navbar a {
    margin: 0 0.8rem;
    font-size: 1.9rem;
    color: var(--main_color);
    padding: 0.4rem;
    /* transition: 0.3s; */
}

header .navbar a:hover,
header .fa-bars:hover {
    color: var(--secondary-color);
    scale: 1.15;
}

header .fa-bars {
    font-size: 2.8rem;
    color: var(--main_color);
    cursor: pointer;
    display: none;
}

/* home section */

.home .home-slide .box {
    position: relative;
    height: 100vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.home .home-slide .box img {
    /* position: absolute; */
    /* top: 0; */
    /* left: 0; */
    /* width: 100%; */
    /* height: 100%; */
    object-fit: cover;
}

.home .home-slide .box::before {
    content: "";
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

}

.home .home-slide .box .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--main_color);
}

.home .home-slide .box .content h3 {
    font-size: 4rem;
}

.home .home-slide .box .content p {
    font-size: 2.5rem;
    margin: 1rem 0;
}

/* about  */

.about {
    background: #eee;
}

.about .box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin-top: 7rem;
}

.about .box .image img {
    width: 60rem;
    height: 100%;
    object-fit: cover;
    
}
.about .box .content h3{
    color: var(--Sale_color);
    font-size: 3.5rem;
}
.about .box .content p{
    color: var(--Sale_color);
    font-size: 2rem;
    margin: 2rem 0;
}
.about .box .content div{
    color: var(--Sale_color);
    font-size: 2.2rem;
}

/* service  */
.services .row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
    gap: 0.8rem;
    margin-top: 5rem;
    
}

.services .row .box {
    display: flex; /* new */
    flex-direction: column; /* new */
    align-items: center;
    color: var(--Sale_color);
    padding: 4rem;
    border-radius: 0.8rem;
}
.services .row .box:hover{
    box-shadow: 0.5rem 0.5rem 0.5rem 0.5rem rgba(0, 0, 0, 0.2);
    transform: scale(1.05)
}
.services .row .box i{
    font-size: 5rem;
}
.services .row .box h3 {
    font-size: 2.5rem;
    margin: 1.5rem 0;
    
}
.services .row .box p {
    font-size: 2rem;
    text-align: center;
    
}
/* portfolio  */

.portfolio .row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25%, 1fr));
    gap: 1rem;
    margin: 6rem auto;
    width: 90%;

}
.portfolio .row .box {
    position: relative;
    overflow: hidden;; 
}
.portfolio .row .box:hover img{
    transform: translateY(-100%);
}
.portfolio .row .box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* transition: all 0.5s ease; */
}
.portfolio .row .box:hover .content{
    transform: translateY(0);
}
.portfolio .row .box .content{
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    text-align: center;
    padding: 3rem 2rem;
    height: 100%;
    width: 100%;
    transform: translateY(100%);
    /* transition: all 0.5s ease; */
    padding-top: 2.5rem;

}
.portfolio .row .box .content h3{
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-top: 4rem;
}
.portfolio .row .box .content p{
    padding: 1rem 0;
    font-size: 1.8rem;
    line-height: 1;
    color: var(--primary-color);
    margin-bottom: 0;
    color: var(--primary-color);

}
.portfolio .row .box .content .btn{ /* new  */
    margin-top: 1rem;
}

/* team  */
.team {
    width: 100%;
}
.team .row {
    background: url(../img/banner_home1.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    position: relative;
    display: flex;
    align-items: center;
    min-height: 70vh;
    /* justify-content: center; */
}
.row::before{
    content: "";
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

}
.team .row .team-slide .box{
    padding: 0 3%;
}
.team .team-slide .box .content{
    background: #222;
    box-shadow: 0.5rem 0.5rem 0.5rem 0.5rem rgba(0, 0, 0, 0.7);
    text-align: center;
    padding: 4rem;
    border-radius: 1rem;
    height: 30rem;

}

.team .team-slide .box .image img {
    width: 15rem;
    height: 15rem;
    border-radius: 50%;
    position: absolute;
    top: 14rem;
    left: 1rem;
    border: 0.3rem solid var(--secondary-color);
}
/* .team .team-slide .box .image img:hover {
    transform: scale(1.1);
    transition: 0.5s;
} */
/* new  */
.team .team-slide .box .content h3 {
    font-size: 3.5rem;
    color: var(--Sale_color);
    margin: 1rem;
}
.team .team-slide .box .content p{
    color: #fff;
    font-size: 2rem;
    margin: 1rem;
}


/* blog  */

.blog .row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25%, 1fr));
    gap: 2rem;
    margin: 6rem 0;
}
.blog .row img {
    width: 100%;
    min-height: 18rem;  /* new*/
    object-fit: cover;
}
.blog .row h3 {
    color: var(--Sale_color);
    font-size: 3rem;
    margin: 1rem 0;
}

.blog .row p {
    color: var(--secondary-dark-color);
    font-size: 1.9rem;
    margin: 1rem 0;
}

/* welcome  */
.welcome {
    padding: 7rem 0;
    text-align: center;
    height: 18rem;
    background: var(--secondary-dark-color);
}
.welcome span {
    font-size: 3rem;
    padding: 3rem;
    color: var(--primary-color);
}

/* contact  */

.contact .box {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
    gap: 2rem;
    text-align: center;
}
.contact .box i{
    font-size: 3.8rem;
    color: var(--primary-color);
}
.contact .box div{
    font-size: 2rem;
    color: var(--Sale_color);
    margin: 1rem 0;
}



/* form  */



.contact .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin: 8rem 0;
}
.contact .row .form-c input,textarea {
    background: var(--light-bg-color);
    border: 0.1rem solid rgb(160, 160, 160);
    padding: 1rem;
    width: 100%;

    height: 4.5rem;
    margin: 0.7rem 0;
    color: var(--Sale_color);
    font-size: 1.9rem;
}
.contact .row .form-c input[type="submit"]{
    background: var(--secondary-color);
    cursor: pointer;
    margin-bottom: 2rem;
    color: var(--primary-color);
    border: 0.1rem solid var(--secondary-color);
}
.contact .row .form-c input[type="submit"]:hover{
    background: none;

}
.contact .row .form-c textarea {
    height: 15rem;
    padding: 1rem;
}












/* media query  */
@media(max-width: 750px) {

    header .fa-bars {
        display: block;
    }

    header .navbar {
        position: fixed;
        /* top: 7.5rem; */
        top: 1000rem;
        left: 0;
        text-align: center;
        background: rgba(10, 10, 9, 0.6);
        width: 100%;
    }

    header .navbar.active {
        top: 7.5rem;
    }

    header .navbar a {
        display: block;
        margin: 0.3rem 0;
        padding: 1rem;
        font-size: 2.6rem;
        color: var(--main_color);
    }

    header .navbar a:hover {
        color: var(--secondary-color);
        transition: ease-in-out 0.3s;
        scale: 1.1;
        background: var(--light-bg-color);
    }
    .about .box {
        flex-wrap: wrap;
        text-align: center;
        justify-content: center;
    }
    .about .box .image img { 
        width: 90%;
    }
    .services .row {
        grid-template-columns: repeat(auto-fit, minmax(80%, 1fr));
   
    }
    .blogv.row {
        grid-template-columns: repeat(auto-fit, minmax(80%, 1fr));

    }

    .contact .row {
        width: 100%;
    }




}

@media(max-width: 550px) {
    .portfolio .row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(50%, 1fr));
    }

}
