@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

:root {
    --primary-color: #30a8b8;
    --secondary-color: #7af75b;
    --secondary-dark-color: #79800559;
    --black: #130f40;

    --main_color: #ff8716;
    --p_color: #7b7b7b;
    --bg_color: #f3f3f3;
    --white_color: #fff;
    --color_heading: #121416;
    --border_color: #e5e5e5d5;
    --Sale_color: #E51A1A;


    --light-bg-color: #f2f3f5;
    --light-text-color: #7c899a;
    --border-color: #e5e8ec;
    --dark-color: #0a021c;

    --font-small: 13px;
    --font-smaller: 11px;

    --percent100: 100%;
    --percent50: 50%;

    --fw3: 300;
    --fw5: 500;
    --fw6: 600;
    --fw7: 700;
    --fw8: 800;

    /* --trans-background-color: background-color .3s, color .3s;
    --trans-background: background-color .3s;
    --trans-color: color .3s; */


}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    outline: none;
    border: none;
    text-decoration: none;
    text-transform: capitalize;
    /* text-align: center;  */
    /* list-style: none; */
    font-family: "Inter", sans-serif;
    /* color: var(--color_heading); */
    transition: all .2s linear;
}





/*-----------------------
 * REUSABLE SELECTION End --
  *----------------------*/

body{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: var(--light-bg-color);
    color: var(--light-text-color);
}


.temp-image{
    margin: 0 auto;
    width: 70%;
}
.temp-image h1{
    font-size: 1.9rem;
    color: var(--primary-color);
    text-align: center;
    margin: 1rem;

}
.temp-image img {
    width: 100%;
    object-fit: cover;
}

.temp-image p{
    font-size: 1.25rem;
    color: var(--primary-color);
    text-align: center;
}

