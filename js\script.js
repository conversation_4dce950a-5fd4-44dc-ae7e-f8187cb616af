let Navbar = document.querySelector('.navbar');
let Fabars = document.querySelector('.fa-bars');

Fabars.onclick = () => {
    Navbar.classList.toggle("active")
};



// swiper 
    var swiper = new Swiper(".home-slide", {
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      
        autoplay: {
            delay: 2500,
            disableOnInteraction: false
        },
        loop: true,
        mousewheel: false,
        keyboard: true
    });



// team swiper 

var swiper = new Swiper(".team-slide", {
    loop: true,
    slidesPerView: 1,
    spaceBetween: 10,
    breakpoints: {
        "0": {
            slidesPerView: 1,
            // autoplay: {
            //     delay: 700,
            //     disableOnInteraction: false,
            // },
        },
        "768": {
            slidesPerView: 3,
        },
        "1020": {
            slidesPerView: 6,
        },
    },
    autoplay: {
    delay: 1000,
    disableOnInteraction: false
    },
    mousewheel: false,
    keyboard: true

});

// modal 
const box = document.querySelector(".box"),
     overlay = document.querySelector(".overlay"),
    //  showMore = document.querySelector(".show-more"),
     closeBtn = document.querySelector(".close-btn");

    //  showMore.addEventListener("click", () => box.classList.add("active"));

// add active class to the parent box when clicking on show more button 
document.querySelectorAll('.show-more').forEach(btn => {

    btn.addEventListener('click', function() {
        const parentBox = this.closest('.box');
        if (parentBox) {
            parentBox.classList.add('active');
        }
    });
});

// remove active class from the parent box when clicking on close button 
document.querySelectorAll('.close-btn').forEach(btn => {

    btn.addEventListener('click', function() {
        const parentBox = this.closest('.box');
        if (parentBox) {
            parentBox.classList.remove('active');
        }
    });
});

// document.querySelectorAll('.show-more').forEach(btn => {
//     btn.addEventListener('click', function() {
//         const parentBox = this.closest('.box');
//         if (parentBox) {
//             parentBox.classList.remove('active');
//         }
//     });
// });

