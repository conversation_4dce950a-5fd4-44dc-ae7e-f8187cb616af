// Navbar functionality
const navbar = document.querySelector('.navbar');
const menuToggle = document.querySelector('.menu-toggle');
const navLinks = document.querySelectorAll('.nav-link');

// Toggle mobile menu
menuToggle.addEventListener('click', () => {
    navbar.classList.toggle('active');

    // Change hamburger icon to X when menu is open
    const icon = menuToggle.querySelector('i');
    if (navbar.classList.contains('active')) {
        icon.classList.remove('fa-bars');
        icon.classList.add('fa-times');
    } else {
        icon.classList.remove('fa-times');
        icon.classList.add('fa-bars');
    }
});

// Close mobile menu when clicking on nav links
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        navbar.classList.remove('active');
        const icon = menuToggle.querySelector('i');
        icon.classList.remove('fa-times');
        icon.classList.add('fa-bars');
    });
});

// Close mobile menu when clicking outside
document.addEventListener('click', (e) => {
    if (!navbar.contains(e.target) && !menuToggle.contains(e.target)) {
        navbar.classList.remove('active');
        const icon = menuToggle.querySelector('i');
        icon.classList.remove('fa-times');
        icon.classList.add('fa-bars');
    }
});

// Smooth scrolling for navigation links
navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href');

        if (targetId.startsWith('#')) {
            const targetSection = document.querySelector(targetId);
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        }
    });
});

// Active navigation highlighting
function updateActiveNav() {
    const sections = document.querySelectorAll('section[id]');
    const headerHeight = document.querySelector('.header').offsetHeight;

    sections.forEach(section => {
        const sectionTop = section.offsetTop - headerHeight - 100;
        const sectionBottom = sectionTop + section.offsetHeight;
        const scrollPosition = window.scrollY;

        if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${section.id}`) {
                    link.classList.add('active');
                }
            });
        }
    });

    // Handle home section specially (when at top of page)
    if (window.scrollY < 100) {
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#home') {
                link.classList.add('active');
            }
        });
    }
}

// Update active nav on scroll
window.addEventListener('scroll', updateActiveNav);

// Initialize active nav on page load
document.addEventListener('DOMContentLoaded', updateActiveNav);



// swiper 
    var swiper = new Swiper(".home-slide", {
      navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      
        autoplay: {
            delay: 2500,
            disableOnInteraction: false
        },
        loop: true,
        mousewheel: false,
        keyboard: true
    });



// team swiper 

var swiper = new Swiper(".team-slide", {
    loop: true,
    slidesPerView: 1,
    spaceBetween: 10,
    breakpoints: {
        "0": {
            slidesPerView: 1,
            // autoplay: {
            //     delay: 700,
            //     disableOnInteraction: false,
            // },
        },
        "768": {
            slidesPerView: 3,
        },
        "1020": {
            slidesPerView: 6,
        },
    },
    autoplay: {
    delay: 1000,
    disableOnInteraction: false
    },
    mousewheel: false,
    keyboard: true

});

// modal 
const box = document.querySelector(".box"),
     overlay = document.querySelector(".overlay"),
    //  showMore = document.querySelector(".show-more"),
     closeBtn = document.querySelector(".close-btn");

    //  showMore.addEventListener("click", () => box.classList.add("active"));

// add active class to the parent box when clicking on show more button 
document.querySelectorAll('.show-more').forEach(btn => {

    btn.addEventListener('click', function() {
        const parentBox = this.closest('.box');
        if (parentBox) {
            parentBox.classList.add('active');
        }
    });
});

// remove active class from the parent box when clicking on close button 
document.querySelectorAll('.close-btn').forEach(btn => {

    btn.addEventListener('click', function() {
        const parentBox = this.closest('.box');
        if (parentBox) {
            parentBox.classList.remove('active');
        }
    });
});

// document.querySelectorAll('.show-more').forEach(btn => {
//     btn.addEventListener('click', function() {
//         const parentBox = this.closest('.box');
//         if (parentBox) {
//             parentBox.classList.remove('active');
//         }
//     });
// });

