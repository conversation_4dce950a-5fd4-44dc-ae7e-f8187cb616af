@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&display=swap');

@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@100..900&family=Reem+Kufi:wght@400..700&display=swap');

:root {
    --primary-color: #014874;
    --secondary-color: #0092df;
    --third-color: #30a8b8;
    --darkblue: #0b0360;
    --darkblue-lighter: #040338;

    
    
    --white-color: #fff;
    
    --border_color: #e5e5e5d5;


    --light-bg-color: #edb494;
    --light-text-color: #7c899a;
    --border-color: #e5e8ec;
    --dark-color: #0a021c;

    --font-small: 13px;
    --font-smaller: 11px;

    --percent100: 100%;
    --percent50: 50%;

    --fw3: 300;
    --fw5: 500;
    --fw6: 600;
    --fw7: 700;
    --fw8: 800;

    /* --trans-background-color: background-color .3s, color .3s;
    --trans-background: background-color .3s;
    --trans-color: color .3s; */


}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    outline: none;
    border: none;
    text-decoration: none;
    text-transform: capitalize;
    width: 100%;
    text-align: center; 
    /* list-style: none; */
    font-family: "Noto Kufi Arabic", sans-serif;
    /* color: var(--color_heading); */
    transition: all .2s linear;
}
html {
    scroll-behavior: smooth;
    scroll-padding-top: 0.5rem;
    font-size: 66%;
    width: 96vw;
}

body{
    width: 100%;
    margin: auto;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--darkblue);
}
h2{
    font-size: 2.5rem;
}
h3{
    font-size: 2rem;
}
p{
    color: var(--darkblue-lighter);
    text-transform: lowercase;
    font-size: 1.5rem;
}
.btn {
    display: inline-block;
    padding: 0.8rem 3.8rem;
    font-size: 1.8rem;
    border: 0.3rem solid var(--primary-color);
    outline: 0;
    background: var(--white-color);
    border-radius: 1rem;
    cursor: pointer;
    color: var(--white_color);
    text-align: center;
    /* color: var(--main_color); */
}

.btn:hover {
    background: none;
    color: var(--secondary-color);
    border: 0.5rem solid var(--secondary-color);
}
button.show-more{
    position: absolute;
    bottom: 2rem;
    width: 35%;
    height: 5rem;
    border-radius: 1rem;
    border: 0.3rem solid var(--primary-color);
    background: var(--white-color);
    color: var(--darkblue);
    font-size: 1.8rem;
    cursor: pointer;
    transition: 0.3s;
}
button.show-more:hover{
    background: var(--primary-color);
    color: var(--white-color);
}
.box.active .show-more{
    display: none;
}
.overlay{
    position: fixed;
    /* right: 2%; */
    right: 0;
    /* top: 15%; */
    top: 0;
    width: 100%;
    min-height: 100vh;
    /* border-radius: 24px; */
    background: rgba(0, 0, 0, 0.6);
    opacity: 0;
    pointer-events: none;
    z-index: 1;

}
.box.active .overlay{
    opacity: 1;
    pointer-events: auto;
}
.modal-box{
    position: fixed;
    top: 20%;
    right: 7.5%;
    /* left: 3%; */
    /* bottom: 7%; */
    /* bottom: 12%; */
    width: 85%;
    min-height: 70vh;
    /* height: 98%; */
    /* margin: 2% 0; */
    z-index: 1;
    /* display: flex; */
    /* flex-direction: column; */
    align-items: center;
    /* max-width: 80%; */

    padding: 3rem 2rem;
    border-radius: 20px;
    /* gap: 3rem; */
    background-color: #fff;
    border: 0.25rem solid var(--primary-color);
    opacity: 0;
    pointer-events: none;
    transition: 0.3s;
}
.box.active .modal-box{
    opacity: 1;
    pointer-events: auto;
}
.modal-box .close-btn i {
    font-size: 2.2rem;
    text-align: center;
    align-items: center;
    justify-content: center;
    display: flex;
    font-size: 2rem;
}
.modal-box i{
    font-size: 3rem;
    color: var(--darkblue);
}
.modal-box h4{
    margin-top: 2rem;
    font-size: 2rem;
    font-weight: var(--fw6);
    color: #333;
}
.modal-box h5{
    font-size: 1.5rem;
    font-weight: var(--fw5);
    color: #333;
    text-align: center;
}
.modal-box button{
        width: fit-content;
    position: absolute;
    right: 25px;
    top: 25px;
}

.modal-box button i {
        /* position: relative; */
    font-size: 2.2rem;
    height: 3rem;
    width: 3rem;
    border-radius: 50%;
    border: 0.3rem solid var(--darkblue);
    background: var(--white-color);

}



.heading {
    margin: 0 auto;
    font-size: 3.5rem;
    text-align: center;
    color: var(--primary-color);
    margin-top: 2.5rem;
}

.heading div {
    display: inline-block;
    border-top: 0.4rem solid var(--primary-color);
    width: 8rem;
    height: 3.5rem;
}

.heading div:hover {
    width: 25rem;
}

section {
    /* padding: 0.5rem 2%; */
    width: 100%;
}



/*-----------------------
 * REUSABLE SELECTION End --
  *----------------------*/

/* header  */
header {
    position: fixed;
    top: 0;
    right: 0;
    left: 0rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--white-color);
    z-index: 1000;
    padding: 1rem 0rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}
header .logo{
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: fit-content;
    margin-left: 1.5rem;
    /* gap: 0; */
}
.icon {

    height: 6.5rem;
    /* object-fit: cover; */
    /* cursor: pointer; */
    /* transition: 0.3s; */
    /* position: absolute; */
    /* top: 10px */
}
header img {
    margin-top: 8px;
    /* width: 150px; */
    /* object-fit: cover; */
}


header .navbar {
    display: flex;
    gap: 0.9rem;
    margin-left: auto;
    margin-right: 2rem;
    align-items: center;
    justify-content: end;

}

header .navbar a {
    margin: 0 0.8rem;
    font-size: 1.45rem;
    width: max-content;
    color: var(--primary-color);
    padding: 0.4rem;
    transition: all 0.3s ease;
    font-weight: 900;
    text-decoration: none;
    position: relative;
}

header .navbar a:hover,
header .navbar a.active {
    color: var(--secondary-color);
    transform: scale(1.05);
}

header .navbar a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background-color: var(--secondary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

header .navbar a:hover::after,
header .navbar a.active::after {
    width: 100%;
}

.menu-toggle {
    display: none;
    cursor: pointer;
    margin-right: 2rem;
}

.menu-toggle .fa-bars {
    font-size: 2.8rem;
    color: var(--primary-color);
    border: 0.2rem solid var(--primary-color);
    border-radius: 5px;
    padding: 1rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.menu-toggle .fa-bars:hover {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: scale(1.1);
}
header .nav-contacts {
    display: flex;
    width: fit-content;
    gap: 1rem;
}
header .nav-contacts .content {
    display: flex;
    flex-direction: row;
    flex-basis: content;
    gap: 1rem;
    font-size: 1.45rem;
    margin: 0 15px;
    font-weight: 900;
    color: var(--primary-color);
}
header .nav-contacts .content i {
    width: 10px;
    padding-top: 5px;
}

header .nav-contacts .content p {
    text-align: left;
    margin-left: 0.5rem;
    font-size: 1.25rem;
    font-weight: var(--fw5);
    color: var(--primary-color);
}


/* home section */
.home {

    margin: 15rem 2.5rem 0 2.5rem;
}
/* 
.home .row::before {
    content: "";
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;


 } */
.home .home-slide .box {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 80vh;
    width: 100%;
    /* border-radius: 10px; */
    padding: 0;
    /* min-height: 300px; */
    align-items: center;
    justify-content: center;
    overflow: hidden;
}
.home .home-slide .box .image{
    width: 100%;
    overflow: hidden;
}

.home .home-slide .box img {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 98%;
    height: 90%;
    border-radius: 10px;

    object-fit: cover;
}

 .home .home-slide .box::before {
    content: "";
    background:white;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

}

.home .home-slide .box .content {
    display: flex;
    flex-direction: column;
    position: absolute;
    bottom: 5rem;
    gap: 0.6rem;
    padding: 2rem;
    line-height: 1.3;
    z-index: 1;
    color: var(--primary-color);
    align-items: center;
}





.home .home-slide .box .content h3 {
    border-radius: 18px;
    padding: 5px 20px;
    font-size: 2.5rem;
    font-weight: var(--fw8);
    /* -webkit-text-stroke: 1.5px var(--white-color); */
    background-color: var(--white-color);
    width: fit-content;
    opacity: 0.7;
    /* text-align: center; */
}

.home .home-slide .box .content p {
    border-radius: 18px;
    font-size: 1.8rem;
    font-weight: var(--fw5);
    background-color: var(--white-color);
    padding: 5px 20px;
    width: fit-content;
    /* -webkit-text-stroke: 0.5px var(--white-color); */
    opacity: 0.7;

}

.home .home-slide .swiper-button-next,
.home .home-slide .swiper-button-prev{
    color: var(--primary-color);
    font-size: 4.5rem;
    -webkit-text-stroke: 1px var(--white-color);
    margin: 0 2rem;
}

.home .home-slide .swiper-pagination-bullets-active{
    background: var(--primary-color);    
    font-size: 4.5rem;
    -webkit-text-stroke: 2px var(--secondary-color);
}
    /* about  */

.about {
    background: var(--white-color);
}

.about .box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}
.about .box .image {
    height: 100%;
    padding-left: 2rem;

}
.about .box .image img {
 
    width: 30rem;
    margin-bottom: 1.7rem;
    padding-left: 5rem;

    border-radius: 10px;
    padding: 0 5rem;
    display: flex;
    
}

.about .box .image h3{
    padding-left: 5rem;
    font-weight: var(--fw8);
    color: var(--darkblue);
    text-align: left;
}

.about .box .image p{
    font-size: 2rem;
    padding-left: 5rem;

    font-weight: var(--fw5);
    color: var(--darkblue);
    margin: 1rem 0;
    text-align: left;

}
.about .box .content h3{
    color: var(--darkblue);
    padding-left: 5rem;
    text-align: left;
    
}
.about .box .content p{
    color: var(--darkblue);
    padding-left: 5rem;
    text-align: left;
    
    font-size: 2rem;
    margin: 2rem 0;
}
.about .box .content div{
    color: var(--Sale_color);
    font-size: 2.2rem;
}

/* service  */
.services .row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
    gap: 1.8rem;
    
}

.services .row .box {
    display: flex; /* new */
    position: relative;
    flex-direction: column; /* new */
    height: 450px;
    align-items: center;
    padding: 4rem;
    border-radius: 0.8rem;
    border: 0.1rem solid var(--third-color);
  
}
.services .row .box:hover{
    box-shadow: 0.5rem 0.5rem 0.5rem 0.5rem rgba(0, 0, 0, 0.3);
    /* transform: scale(1.05) */
}
.services .row .box i{
    font-size: 5rem;
}
.services .row .box h3 {
    margin: 1rem 0;
    text-align: center;
    
}
.services .row .box p {
    line-height: 1.8;
    direction: rtl;
      overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
 
    
}
.services .row .btn{
    position: absolute;
    bottom: 20px;
    width: 35%;
    padding: 0.7rem;
}

/* portfolio  */

.portfolio .row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25%, 1fr));
    gap: 4rem;
    margin: 2.5rem;
    /* width: 90%; */

}
.portfolio .row .box {
    position: relative;
    overflow: hidden;; 
}
.portfolio .row .box:hover img{
    /* transform: translateY(-100%); */
    opacity: 0.9;
}
.portfolio .row .box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* transition: all 0.5s ease; */
}
.portfolio .row .box:hover .content{
    transform: translateY(0);
}
.portfolio .row .box .content{
    position: absolute;
    top: 0;
    left: 0;
    background: var(--white-color);
    opacity: 0.6;
    text-align: center;
    padding: 3rem 2rem;
    height: 100%;
    width: 100%;
    transform: translateY(100%);
    transition: all 0.5s ease;
    padding-top: 2.5rem;

}
.portfolio .row .box .content h3{
    font-size: 2.5rem;
    font-weight: var(--fw8);
    color: var(--dark-color);
}
.portfolio .row .box .content p{
    padding: 1rem 0;
    font-size: 1.8rem;
    font-weight: var(--fw5);
    line-height: 1;
    color: var(--dark-color);
    margin-bottom: 0;

}
.portfolio .row .box .content .btn{ /* new  */
    margin-top: 1rem;
}

/* team  */
.team {
    width: 100%;
    margin: 7rem 2.5rem;
}
.swiper-android .swiper-slide, .swiper-ios .swiper-slide, .swiper-wrapper {
    /* padding: 15px; */
}
.team .row {
    /* background: url(../img/banner_home1.png); */
    /* background-position: center; */
    /* background-repeat: no-repeat; */
    /* background-size: cover; */
    /* background-attachment: fixed; */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 20vh;
    
}
.team .row .team-slide .swiper-horizontal {
    padding: 15px;
}
.team.row::before{
    content: "";
    background: rgba(215, 201, 201, 0.6);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;

}
.team .row .team-slide .box{
    padding: 0 2rem;
}
.team .team-slide .box .content{
    box-shadow: 0.5rem 0.5rem 0.5rem 0.5rem rgba(199, 193, 193, 0.7);
    text-align: center;
    border-radius: 2rem;
    height: 10rem;
    width: 21rem;
    overflow: hidden;

}

.team .team-slide .box .image img {
    height: 8rem;
    width: 20rem;
    position: absolute;
    top: 13px;
    left: 28px;
    /*  border: 0.1rem solid var(--primary-color); */
}
/* .team .team-slide .box .image img:hover {
    transform: scale(1.1);
    transition: 0.5s;
} */
/* new  */
.team .team-slide .box .content h3 {
    color: var(--darkblue);
    margin: 10px 0 0 0;
}
.team .team-slide .box .content p{
    color: var(--darkblue-lighter);
    font-size: 1.75rem;
    margin: 10px 0 0 0;

    text-align: right;
    direction: rtl;
}


/* blog  */

.blog .row {
    /* display: grid; */
    /* grid-template-columns: repeat(auto-fit, minmax(25%, 1fr)); */
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    /* position: relative; */
    gap: 2rem;
    margin: 6rem 0;
}
.blog .row .box{
 position: relative;
 /* width : 30%; */
 display: flex;
 flex-direction: column;
  justify-content: center;
  align-items: center;

    flex: 1 0 calc(30% - 2rem);
}



.blog .row img {
    /* width: 100%; */
    height: 250px;
    width: 100%;
    object-fit: cover;
}
.blog .row h3 {
    color: var(--Sale_color);
    margin: 1rem 0;
 direction: rtl;

}

.blog .row p {
    color: var(--secondary-dark-color);
    margin: 1rem 0;
 direction: rtl;

}
.blog .row .box .content .btn{
 width: 35%;
 padding: 0.75rem;
    
}

/* welcome  */
.welcome {
    text-align: inherit;
    height: 17rem;
    margin-bottom: 7rem;
    /* background: var(--secondary-dark-color); */
    
}
.welcome span {
    font-size: 6rem;
    font-weight: var(--fw6);
    padding: 3rem;
    color: var(--darkblue);
  font-family: "Reem Kufi", sans-serif;
}

/* contact  */

.contact .box {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
    gap: 2rem;
    text-align: center;
}
.contact .box i{
    font-size: 3.8rem;
    color: var(--darkblue);
}
.contact .box div{
    font-size: 2rem;
    color: var(--darkblue);
    margin: 1rem 0;
}



/* form  */



.contact .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin: 8rem 0;
}
.contact .row .ifram {
    position: relative;
    width: 95%;
    height: 100%;
    margin: 0 1rem;

}

    .contact .row .form-c input,textarea {
    background: var(--white-color);
    border: 0.1rem solid rgb(160, 160, 160);
    padding: 1rem;
    width: 100%;
    border-radius: 0.5rem;
    padding-left: 25px;

    height: 4.5rem;
    margin: 0.7rem 0;
    font-size: 1.9rem;
}

.contact .row .form-c input:focus,textarea:focus {
    border: 2px solid var(--primary-color);

}


.contact .row .form-c input[type="submit"]{
    background: var(--secondary-color);
    cursor: pointer;
    margin-bottom: 2rem;
    color: var(--primary-color);
    border: 0.1rem solid var(--secondary-color);
}
.contact .row .form-c input[type="submit"]:hover{
    background: none;

}
.contact .row .form-c textarea {
    height: 15rem;
    padding: 1rem;
}
.contact .row .form-c button{
    /* position: relative; */
    /* left: 25%;; */
    background: var(--secondary-color);
    cursor: pointer;
    margin: 2rem auto;
    width: 40%;
    align-items: center;
    color: var(--primary-color);
    border: 0.1rem solid var(--secondary-color);
    padding: 1rem 2rem;
    font-size: 1.9rem;
    border-radius: 0.5rem;
}
.contact .row .form-c button:hover{
    background: none;

}

/* footer  */
footer {
    background: var(--white-color);
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    padding: 1rem;
    border-radius: 1.5rem;
}
footer .content h3 {
    text-align: left;
    text-align: center;
line-height: 0.7;
    color: var(--darkblue);
}
footer .content .logo-footer{
    /* display: flex; */
    /* position: relative; */
    /* flex-direction: column; */
    /* height: 40%; */
    /* justify-content: center; */
    /* align-items: center; */
    /* gap: 1rem; */
    /* padding: 6rem; */
    /* border: 0.1rem solid var(--darkblue); */
    border-radius: 2.5rem;
    color: var(--white);
    background: white;
    overflow: hidden;
    padding: 0;
    margin: 0;
    
}

footer .content .logo-footer img {
    margin-top: 7px;
    margin-left: -7px;
    height: 125px;
    width: 180px;
}
footer .content,
footer .our-services{
    flex: 1 0 calc(22% - 0rem);
}
footer .links {
    flex: 1 0 calc(22% - 0rem);
    padding-left: 10rem;
    margin-top: -2rem;
}


footer .links h3,
footer .newsletter h3{
    text-align: left;
    margin-left: 25px;

    color: var(--darkblue-lighter);

}
footer .links h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}


footer .our-services .content {
    
    display: flex;
    flex-direction: row;
    text-align: center;
    margin: 20px 0;
}
footer .our-services div {
    font-size: 12px;
    font-weight: var(--fw8);
    color: var(--darkblue);
    display: flex;
}
footer .our-services div i {
    font-size: 2.2rem;
    margin-right: 0.5rem;
    color: var(--darkblue);
    width: 40px;
}
footer .content p,
.newsletter p{
    font-size: 1.6rem;
    margin: 1rem 0;
    color: var(--darkblue);
}


footer .newsletter i{
    font-size: 2.6rem;
    margin: 3rem 0.5rem;
    color: var(--white-color);
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 6rem;
    width: 6rem;
    border: 0.1rem solid var(--white-color);

}
footer .content i:hover,
footer .content a:hover,
footer .our-services a:hover,
footer .links a:hover{
    color: var(--secondary-color);
}
footer .content a,
footer .our-services a,
footer .links a
{
    display: block;
    font-size: 1.3rem;
    font-weight: var(--fw5);
    /* marg/in: 0.5rem; */
    text-align: left;
    margin-left: 50px;
    color: var(--darkblue);


}
footer .links a i {
    display: inline;
    margin-right: 0.5rem;
}




























/* Tablet responsive styles */
@media(max-width: 1024px) {
    header .navbar {
        margin-left: 2rem;
        gap: 0.5rem;
    }

    header .navbar a {
        font-size: 1.3rem;
        margin: 0 0.5rem;
    }

    header .nav-contacts .content {
        font-size: 1.2rem;
        margin: 0 10px;
    }

    header .nav-contacts .content p {
        font-size: 1.1rem;
    }
}

@media(max-width: 915px) {
    .about .box{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;;
    }
}




/* media query  */
@media(max-width: 750px) {


    h2{
    font-size: 3.5rem;
}
h3{
    font-size: 3rem;
}




    section {
        margin: 0 auto;
        width: 100%;
    }

 

    .menu-toggle {
        display: block;
        text-align: -webkit-right;
    }

    header .navbar {
        position: fixed;
        top: -100vh;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: var(--white-color);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding-top: 8rem;
        z-index: 999;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    header .navbar.active {
        top: 0;
        margin-top: 2rem;
    }

    header .navbar a {
        display: block;
        margin: 1rem 0;
        padding: 1.5rem 2rem;
        font-size: 2.2rem;
        color: var(--primary-color);
        width: 60%;
        text-align: center;
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    header .navbar a:hover,
    header .navbar a.active {
        color: var(--white-color);
        background: var(--secondary-color);
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    header .navbar a::after {
        display: none;
    }

    header .nav-contacts {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        width: 90%;
    }

    header .nav-contacts .content {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        font-size: 1.4rem;
        color: var(--primary-color);
        padding: 1rem;
        background: rgba(var(--primary-color-rgb), 0.1);
        border-radius: 8px;
    }

    header .nav-contacts .content i {
        font-size: 1.6rem;
    }

    header .nav-contacts .content p {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
    }

    .home .home-slide .swiper-button-next,
    .home .home-slide .swiper-button-prev{
        display: none;
    }
    .about .box {
        flex-wrap: wrap;
        text-align: center;
        justify-content: center;
    }
    .about .box .image img { 
        width: 90%;
    }
    .services .row {
        grid-template-columns: repeat(auto-fit, minmax(80%, 1fr));
   
    }
    .blog .row {
        grid-template-columns: repeat(auto-fit, minmax(80%, 1fr));

    }
    .contact .box {
        grid-template-columns: repeat(auto-fit, minmax(80%, 1fr));

        }

    .contact .row {
        flex-wrap: wrap;
        }
    .contact .row {
        width: 100%;
    }
    .contact .row iframe {
        width: 100%;
        margin:  0 10px;
    }
    footer{
        grid-template-columns: repeat(auto-fit,minmax(80%,1fr));
    }

    footer .content,
    footer .our-services,
    footer .links{
        flex: 1 0 calc(55% - 2rem);
    }

    .blog .row .box{
        flex: 1 0 calc(75% - 2rem);
    }

    .portfolio {
        /* display: none; */
        /* display: grid; */
        grid-template-columns: repeat(auto-fit, minmax(60%, 1fr));
    }



}

@media(max-width: 345px) {

.menu-toggle {
    margin-right: 1rem;
}

.menu-toggle .fa-bars {
    width: 50px;
    height: 50px;
    padding: 0.8rem;
    font-size: 2.4rem;
}

footer .content img.footer-logo {
    width: 24px;
}
footer .content .icon.foot {
  
    height: 10%;
}

.home .home-slide .box{
    height: 54vh;
}


.home .home-slide .box .content {
    position: relative;
    margin: 3rem auto;
    line-height: 1.5;
    top: 26%;

}







.home .home-slide .box .content h3{
    font-size: 2rem;
    -webkit-text-stroke: 0.1rem var(--primary-color);
    -moz-text-stroke: 0.1rem 

}

}


@media(max-width: 550px) {
    .portfolio .row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(75%, 1fr));
    }

}
    